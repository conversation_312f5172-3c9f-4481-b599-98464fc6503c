package server

import (
	"context"
	"log"
	"sync"
	"time"
)

type TrafficType string

const (
	TrafficTypeQUIC  TrafficType = "quic"
	TrafficTypeHTTP2 TrafficType = "http2"
)

// TrafficEvent 流量事件
type TrafficEvent struct {
	StreamID     string      `json:"stream_id"` // 改为字符串，支持 QUIC 和 HTTP/2
	ConnectionID string      `json:"connection_id"`
	AuthToken    string      `json:"auth_token"`
	UserID       string      `json:"user_id"`
	Uploaded     int64       `json:"uploaded"`
	Downloaded   int64       `json:"downloaded"`
	Timestamp    time.Time   `json:"timestamp"`
	TargetAddr   string      `json:"target_addr"`
	TrafficType  TrafficType `json:"traffic_type"`
}

// TrafficHandler 流量处理器接口
type TrafficHandler interface {
	HandleTraffic(event *TrafficEvent)
	GetStats() map[string]interface{}
	GetName() string
	Close()
}

// TrafficManager 流量管理器
type TrafficManager struct {
	handlers  []TrafficHandler
	eventChan chan *TrafficEvent
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
	mu        sync.RWMutex
}

// NewTrafficManager 创建新的流量管理器
func NewTrafficManager(parentCtx context.Context, bufferSize int) *TrafficManager {
	if bufferSize <= 0 {
		bufferSize = 2000 // 默认缓冲区大小
	}

	// 基于父context创建子context
	var ctx context.Context
	var cancel context.CancelFunc
	if parentCtx != nil {
		ctx, cancel = context.WithCancel(parentCtx)
	} else {
		// 兼容性：如果没有父context，使用Background
		ctx, cancel = context.WithCancel(context.Background())
	}

	tm := &TrafficManager{
		handlers:  make([]TrafficHandler, 0),
		eventChan: make(chan *TrafficEvent, bufferSize),
		ctx:       ctx,
		cancel:    cancel,
	}

	// 启动事件处理器
	tm.wg.Add(1)
	go tm.eventProcessor()

	return tm
}

// RegisterHandler 注册流量处理器
func (tm *TrafficManager) RegisterHandler(handler TrafficHandler) {
	tm.mu.Lock()
	defer tm.mu.Unlock()
	tm.handlers = append(tm.handlers, handler)
}

// UnregisterHandler 注销流量处理器
func (tm *TrafficManager) UnregisterHandler(name string) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	for i, handler := range tm.handlers {
		if handler.GetName() == name {
			// 关闭处理器
			handler.Close()
			// 从切片中移除
			tm.handlers = append(tm.handlers[:i], tm.handlers[i+1:]...)
			break
		}
	}
}

// UpdateTraffic 更新流量统计（非阻塞，安全版本）
func (tm *TrafficManager) UpdateTraffic(trafficType TrafficType, streamID string, connectionID, authToken, userID, targetAddr string, uploaded, downloaded int64) {
	// 检查 TrafficManager 是否已关闭
	select {
	case <-tm.ctx.Done():
		// TrafficManager 已关闭，直接返回
		return
	default:
	}

	event := &TrafficEvent{
		StreamID:     streamID,
		ConnectionID: connectionID,
		AuthToken:    authToken,
		UserID:       userID,
		Uploaded:     uploaded,
		Downloaded:   downloaded,
		Timestamp:    time.Now(),
		TargetAddr:   targetAddr,
		TrafficType:  trafficType,
	}

	// 安全地发送到 channel，避免向已关闭的 channel 发送
	select {
	case tm.eventChan <- event:
		// 成功发送
	case <-tm.ctx.Done():
		// TrafficManager 已关闭，停止发送
		return
	default:
		// 缓冲区满，尝试背压控制
		select {
		case tm.eventChan <- event:
			// 第二次尝试成功
		case <-tm.ctx.Done():
			// TrafficManager 已关闭
			return
		case <-time.After(1 * time.Millisecond):
			// 仍然发送失败，记录警告但不阻塞调用者
			log.Printf("⚠️  流量事件缓冲区繁忙，丢弃事件: UserID: %s, Stream %s, 上传: %d, 下载: %d", userID, streamID, uploaded, downloaded)
		}
	}
}

// GetAllStats 获取所有处理器的统计信息
func (tm *TrafficManager) GetAllStats() map[string]interface{} {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	allStats := make(map[string]interface{})
	for _, handler := range tm.handlers {
		allStats[handler.GetName()] = handler.GetStats()
	}

	return allStats
}

// GetHandlerStats 获取指定处理器的统计信息
func (tm *TrafficManager) GetHandlerStats(name string) map[string]interface{} {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	for _, handler := range tm.handlers {
		if handler.GetName() == name {
			return handler.GetStats()
		}
	}
	return nil
}

// GetHandlerCount 获取注册的处理器数量
func (tm *TrafficManager) GetHandlerCount() int {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	return len(tm.handlers)
}

// Close 关闭流量管理器（改进版）
func (tm *TrafficManager) Close() {
	// 先取消上下文，通知所有 UpdateTraffic 调用停止
	tm.cancel()

	// 给一些时间让正在进行的 UpdateTraffic 调用完成
	time.Sleep(100 * time.Millisecond)

	// 关闭事件通道
	close(tm.eventChan)

	// 等待事件处理器结束
	tm.wg.Wait()

	// 关闭所有处理器
	tm.mu.Lock()
	for _, handler := range tm.handlers {
		handler.Close()
	}
	tm.handlers = nil
	tm.mu.Unlock()
}

// eventProcessor 事件处理器（在单独的 goroutine 中运行）
func (tm *TrafficManager) eventProcessor() {
	defer tm.wg.Done()

	for {
		select {
		case <-tm.ctx.Done():
			return
		case event, ok := <-tm.eventChan:
			if !ok {
				return
			}
			tm.processEvent(event)
		}
	}
}

// processEvent 处理单个事件
func (tm *TrafficManager) processEvent(event *TrafficEvent) {
	tm.mu.RLock()
	handlers := make([]TrafficHandler, len(tm.handlers))
	copy(handlers, tm.handlers)
	tm.mu.RUnlock()

	// 并发处理所有处理器
	var wg sync.WaitGroup
	for _, handler := range handlers {
		wg.Add(1)
		go func(h TrafficHandler) {
			defer wg.Done()
			defer func() {
				if r := recover(); r != nil {
					// 处理器崩溃时的恢复机制
					log.Printf("❌ 流量处理器 %s 处理事件时崩溃: %v", h.GetName(), r)
				}
			}()
			h.HandleTraffic(event)
		}(handler)
	}
	wg.Wait()
}
