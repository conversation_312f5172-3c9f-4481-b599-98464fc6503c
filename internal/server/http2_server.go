package server

import (
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"log"
	"net"
	"sync"
	"sync/atomic"
	"time"
)

// 全局缓冲池，用于复用读取缓冲区
var bufferPool = sync.Pool{
	New: func() interface{} {
		return make([]byte, 16*1024) // 默认16KB
	},
}

// HTTP2Server SOCKS5 over HTTP2 服务器
type HTTP2Server struct {
	listener          net.Listener
	authManager       *AuthManager
	trafficManager    *TrafficManager
	connectionManager *ConnectionManager
	eventLogger       EventLogger
	ctx               context.Context
	cancel            context.CancelFunc
	wg                sync.WaitGroup
	config            *HTTP2Config
	connPool          *HTTP2ConnPool

	// 统计信息
	startTime              time.Time
	totalHTTP2Connections  int64 // HTTP2连接总数
	activeHTTP2Connections int64 // 活跃HTTP2连接数
	rejectedConnections    int64 // 拒绝的连接数
	totalStreams           int64 // Stream总数
	activeStreams          int64 // 活跃Stream数
	totalBytesUploaded     int64 // 总上传字节数
	totalBytesDownloaded   int64 // 总下载字节数
}

// HTTP2Config HTTP2服务器配置
type HTTP2Config struct {
	ListenAddr      string        // 监听地址
	TLSConfig       *tls.Config   // TLS配置
	MaxConnections  int           // 最大连接数
	MaxStreams      int           // 单连接最大流数
	IdleTimeout     time.Duration // 空闲超时时间
	CleanupInterval time.Duration // 清理间隔
	StatsInterval   time.Duration // 统计输出间隔
	EventLogger     EventLogger   // 事件日志器（可选）
	EnableDebug     bool          // 启用调试模式
	ConnPoolMaxIdle int           // 连接池最大空闲连接数
	ConnPoolIdleTTL time.Duration // 连接池空闲连接TTL
	ConnPoolMaxKeys int           // 连接池最大键数量
}

// HTTP2StreamState HTTP2流状态
type HTTP2StreamState struct {
	dst        net.Conn      // 后端 TCP 连接
	done       chan struct{} // 结束信号
	once       sync.Once     // 确保 done 仅关闭一次
	targetAddr string        // 目标地址
	userID     string        // 用户ID
	authToken  string        // 认证Token（用于流量统计）
	startTime  time.Time     // 开始时间
	server     *HTTP2Server

	BytesUp   int64
	BytesDown int64
}

// 完善上传事件触发
func (s *HTTP2StreamState) uploadBytesAdd(n int64) {
	atomic.AddInt64(&s.BytesUp, n)

	// 触发流量更新事件
	s.server.trafficManager.UpdateTraffic(
		TrafficTypeHTTP2,
		"",
		"",
		s.authToken,
		s.userID,
		s.targetAddr,
		n, // 上传增量
		0, // 下载增量
	)
}

// 完善下载事件触发
func (s *HTTP2StreamState) downloadBytesAdd(n int64) {
	atomic.AddInt64(&s.BytesDown, n)

	// 触发流量更新事件
	s.server.trafficManager.UpdateTraffic(
		TrafficTypeHTTP2,
		"",
		"",
		s.authToken,
		s.userID,
		s.targetAddr,
		0, // 上传增量
		n, // 下载增量
	)
}

func (s *HTTP2StreamState) closeSafely() {
	s.once.Do(func() { close(s.done) })
}

// StreamManager HTTP2流管理器
type StreamManager struct {
	mu         sync.RWMutex
	streams    map[uint32]*HTTP2StreamState
	maxStreams int
	wg         sync.WaitGroup // 跟踪所有readPump goroutine
}

// NewStreamManager 创建新的流管理器
func NewStreamManager(maxStreams int) *StreamManager {
	return &StreamManager{
		streams:    make(map[uint32]*HTTP2StreamState),
		maxStreams: maxStreams,
	}
}

// Add 添加新流
func (sm *StreamManager) Add(streamID uint32, state *HTTP2StreamState) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if len(sm.streams) >= sm.maxStreams {
		return fmt.Errorf("达到最大流数限制: %d", sm.maxStreams)
	}

	sm.streams[streamID] = state
	return nil
}

// Get 获取流
func (sm *StreamManager) Get(streamID uint32) (*HTTP2StreamState, bool) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	stream, exists := sm.streams[streamID]
	return stream, exists
}

// Delete 删除流
func (sm *StreamManager) Delete(streamID uint32) *HTTP2StreamState {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	stream := sm.streams[streamID]
	delete(sm.streams, streamID)
	return stream
}

// Count 获取当前流数量
func (sm *StreamManager) Count() int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	return len(sm.streams)
}

// CheckLimit 检查是否达到流数量限制
func (sm *StreamManager) CheckLimit() bool {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	return len(sm.streams) >= sm.maxStreams
}

// Close 关闭所有流
func (sm *StreamManager) Close() {
	// 1. 关闭所有目标连接，触发readPump退出
	sm.mu.Lock()
	for _, stream := range sm.streams {
		if stream.dst != nil {
			stream.dst.Close()
		}
		stream.closeSafely()
	}
	sm.mu.Unlock()

	// 2. 等待所有readPump goroutine结束
	sm.wg.Wait()

	// 3. 清理流映射
	sm.mu.Lock()
	sm.streams = make(map[uint32]*HTTP2StreamState)
	sm.mu.Unlock()
}

// HTTP2ConnPool HTTP2连接池（用于后端TCP连接）
type HTTP2ConnPool struct {
	mu      sync.Mutex
	idle    map[string][]HTTP2PooledConn // key = "host:port" → 闲置列表
	maxIdle int                          // 每个地址允许的闲置连接
	idleTTL time.Duration                // 闲置多久后回收
	maxKeys int                          // 最大键数量
}

type HTTP2PooledConn struct {
	conn net.Conn
	time time.Time // 最近释放时间
}

// NewHTTP2Server 创建新的HTTP2服务器实例
func NewHTTP2Server(
	parentCtx context.Context, // 新增：父context参数
	config *HTTP2Config,
	authManager *AuthManager,
	trafficManager *TrafficManager,
	connectionManager *ConnectionManager,
	eventLogger EventLogger,
) (*HTTP2Server, error) {
	// 验证必需参数
	if parentCtx == nil {
		return nil, fmt.Errorf("parentCtx不能为nil")
	}
	if config == nil {
		return nil, fmt.Errorf("config不能为nil")
	}
	if authManager == nil {
		return nil, fmt.Errorf("authManager不能为nil")
	}
	if trafficManager == nil {
		return nil, fmt.Errorf("trafficManager不能为nil")
	}
	if connectionManager == nil {
		return nil, fmt.Errorf("connectionManager不能为nil")
	}
	if eventLogger == nil {
		return nil, fmt.Errorf("eventLogger不能为nil")
	}

	// 基于父context创建子context
	ctx, cancel := context.WithCancel(parentCtx)

	// 设置默认值
	if config.MaxStreams == 0 {
		config.MaxStreams = 20
	}
	if config.IdleTimeout == 0 {
		config.IdleTimeout = 15 * time.Second
	}
	if config.CleanupInterval == 0 {
		config.CleanupInterval = 5 * time.Minute
	}
	if config.StatsInterval == 0 {
		config.StatsInterval = 5 * time.Minute
	}
	if config.ConnPoolMaxIdle == 0 {
		config.ConnPoolMaxIdle = 32
	}
	if config.ConnPoolIdleTTL == 0 {
		config.ConnPoolIdleTTL = 2 * time.Minute
	}
	if config.ConnPoolMaxKeys == 0 {
		config.ConnPoolMaxKeys = 10000
	}

	// 创建连接池
	connPool := &HTTP2ConnPool{
		idle:    make(map[string][]HTTP2PooledConn),
		maxIdle: config.ConnPoolMaxIdle,
		idleTTL: config.ConnPoolIdleTTL,
		maxKeys: config.ConnPoolMaxKeys,
	}

	// 创建完整的服务器实例
	server := &HTTP2Server{
		authManager:       authManager,
		trafficManager:    trafficManager,
		connectionManager: connectionManager,
		eventLogger:       eventLogger,
		ctx:               ctx,
		cancel:            cancel,
		config:            config,
		connPool:          connPool,
		startTime:         time.Now(),
	}

	// 注册流量处理器
	server.registerTrafficHandlers()

	return server, nil
}

// registerTrafficHandlers 注册流量处理器
func (s *HTTP2Server) registerTrafficHandlers() {
	// 注册用户级流量处理器
	userHandler := NewUserTrafficHandler(s.authManager)
	s.trafficManager.RegisterHandler(userHandler)

	// 注册HTTP2服务器级流量处理器
	http2Handler := NewHTTP2TrafficHandler(s)
	s.trafficManager.RegisterHandler(http2Handler)
}

// Start 启动HTTP2服务器
func (s *HTTP2Server) Start() error {
	// 创建TLS监听器
	listener, err := tls.Listen("tcp", s.config.ListenAddr, s.config.TLSConfig)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", s.config.ListenAddr, err)
	}

	s.listener = listener

	// 触发服务器启动事件
	s.eventLogger.ServerStart(s.config.ListenAddr, "HTTP2")

	log.Printf("🚀 SOCKS5 over HTTP2 服务器启动成功")
	log.Printf("📡 监听地址: %s", s.config.ListenAddr)
	log.Printf("⚙️  最大连接数: %d", s.config.MaxConnections)
	log.Printf("🎯 单连接最大流数: %d", s.config.MaxStreams)
	log.Printf("⏰ 空闲超时: %v", s.config.IdleTimeout)

	// 启动连接池清理任务
	s.wg.Add(1)
	go s.connPoolJanitor()

	// 启动清理任务
	// s.wg.Add(1)
	// go s.cleanupTask()

	// 启动统计任务
	s.wg.Add(1)
	go s.statsTask()

	// 接受连接
	s.wg.Add(1)
	go s.acceptConnections()

	return nil
}

// Stop 停止HTTP2服务器
func (s *HTTP2Server) Stop() error {
	log.Printf("🛑 正在停止HTTP2服务器...")

	// 首先取消context，通知所有goroutine停止
	s.cancel()

	// 关闭监听器，停止接受新连接
	if s.listener != nil {
		s.listener.Close()
	}

	// 等待所有goroutine结束
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Printf("✅ 所有goroutine已正常结束")
	case <-time.After(10 * time.Second):
		log.Printf("⚠️  等待goroutine超时，强制退出")
	}

	// 触发服务器停止事件
	s.eventLogger.ServerStop()

	// 输出最终统计信息
	s.printFinalStats()

	log.Printf("✅ HTTP2服务器已停止")
	return nil
}

// acceptConnections 接受客户端连接
func (s *HTTP2Server) acceptConnections() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		default:
		}

		// 接受TLS连接
		conn, err := s.listener.Accept()
		if err != nil {
			select {
			case <-s.ctx.Done():
				return
			default:
				log.Printf("❌ 接受连接失败: %v", err)
				s.eventLogger.ServerError(err)
				continue
			}
		}

		atomic.AddInt64(&s.totalHTTP2Connections, 1)
		atomic.AddInt64(&s.activeHTTP2Connections, 1)

		log.Printf("🔗 新HTTP2连接: %s", conn.RemoteAddr())

		// 触发客户端连接事件
		s.eventLogger.ClientConnect(conn.RemoteAddr())

		// 处理HTTP2连接
		s.wg.Add(1)
		go s.handleHTTP2Connection(conn)
	}
}

// handleHTTP2Connection 处理单个HTTP2连接
func (s *HTTP2Server) handleHTTP2Connection(conn net.Conn) {
	connectTime := time.Now()
	defer s.wg.Done()
	defer func() {
		atomic.AddInt64(&s.activeHTTP2Connections, -1)
		conn.Close()

		// 触发客户端断开事件
		duration := time.Since(connectTime)
		s.eventLogger.ClientDisconnect(conn.RemoteAddr(), duration)

		log.Printf("👋 HTTP2连接关闭: %s", conn.RemoteAddr())
	}()

	log.Printf("🔄 开始处理HTTP2连接: %s", conn.RemoteAddr())

	// 创建HTTP2Connection处理器
	h2conn := NewHTTP2Connection(s, conn)

	// 启动连接处理
	err := h2conn.Start()
	if err != nil {
		log.Printf("❌ HTTP2连接启动失败: %v", err)
		// 如果是连接限制错误，记录拒绝原因
		if err.Error() == "注册连接失败: max connections reached" {
			log.Printf("⚠️  达到全局最大连接数限制，拒绝连接: %s", conn.RemoteAddr())
		}
		return
	}

	// 等待连接结束
	<-h2conn.ctx.Done()
}

// 连接池方法
func (p *HTTP2ConnPool) Get(addr string) (net.Conn, error) {
	p.mu.Lock()
	list := p.idle[addr]
	if n := len(list); n > 0 {
		pooledConn := list[n-1]
		p.idle[addr] = list[:n-1]
		p.mu.Unlock()

		// 健康检查
		if err := p.healthCheck(pooledConn.conn); err == nil {
			log.Printf("🔄 复用连接池连接: %s", addr)
			return pooledConn.conn, nil
		}
		pooledConn.conn.Close()
	} else {
		p.mu.Unlock()
	}

	// 创建新连接
	return net.DialTimeout("tcp", addr, 10*time.Second)
}

func (p *HTTP2ConnPool) Put(addr string, conn net.Conn) {
	if tcpConn, ok := conn.(*net.TCPConn); ok {
		tcpConn.SetKeepAlive(true)

		p.mu.Lock()
		// 检查键数量限制
		if len(p.idle) >= p.maxKeys && p.idle[addr] == nil {
			// 删除一个随机键（简单LRU近似）
			for key := range p.idle {
				delete(p.idle, key)
				break
			}
		}

		list := p.idle[addr]
		if len(list) < p.maxIdle {
			p.idle[addr] = append(list, HTTP2PooledConn{
				conn: conn,
				time: time.Now(),
			})
			conn = nil // 转移所有权
		}
		p.mu.Unlock()
	}

	if conn != nil {
		conn.Close()
	}
}

func (p *HTTP2ConnPool) healthCheck(conn net.Conn) error {
	conn.SetReadDeadline(time.Now().Add(1 * time.Millisecond))
	var one [1]byte
	_, err := conn.Read(one[:])
	if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
		conn.SetReadDeadline(time.Time{})
		return nil
	}
	return errors.New("connection is dead")
}

// connPoolJanitor 连接池清理任务
func (s *HTTP2Server) connPoolJanitor() {
	defer s.wg.Done()

	ticker := time.NewTicker(s.config.ConnPoolIdleTTL / 2)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			now := time.Now()
			s.connPool.mu.Lock()
			for addr, list := range s.connPool.idle {
				keep := list[:0]
				for _, pooledConn := range list {
					if now.Sub(pooledConn.time) < s.config.ConnPoolIdleTTL {
						keep = append(keep, pooledConn)
					} else {
						pooledConn.conn.Close()
					}
				}
				if len(keep) == 0 {
					delete(s.connPool.idle, addr)
				} else {
					s.connPool.idle[addr] = keep
				}
			}
			s.connPool.mu.Unlock()
		}
	}
}

// statsTask 统计任务
func (s *HTTP2Server) statsTask() {
	defer s.wg.Done()

	ticker := time.NewTicker(s.config.StatsInterval)
	defer ticker.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.printStats()
		}
	}
}

// printStats 打印统计信息
func (s *HTTP2Server) printStats() {
	uptime := time.Since(s.startTime)
	totalConns := atomic.LoadInt64(&s.totalHTTP2Connections)
	activeConns := atomic.LoadInt64(&s.activeHTTP2Connections)
	rejectedConns := atomic.LoadInt64(&s.rejectedConnections)
	totalStreams := atomic.LoadInt64(&s.totalStreams)
	activeStreams := atomic.LoadInt64(&s.activeStreams)
	uploaded := atomic.LoadInt64(&s.totalBytesUploaded)
	downloaded := atomic.LoadInt64(&s.totalBytesDownloaded)

	log.Printf("📊 === HTTP2服务器统计信息 ===")
	log.Printf("⏱️  运行时间: %v", uptime)
	log.Printf("🔗 HTTP2连接 - 总计: %d, 活跃: %d, 拒绝: %d", totalConns, activeConns, rejectedConns)
	log.Printf("🎯 HTTP2流 - 总计: %d, 活跃: %d", totalStreams, activeStreams)
	log.Printf("📈 数据传输 - 上传: %s, 下载: %s", formatBytes(uploaded), formatBytes(downloaded))
	log.Printf("========================")
}

// printFinalStats 打印最终统计信息
func (s *HTTP2Server) printFinalStats() {
	uptime := time.Since(s.startTime)
	totalConns := atomic.LoadInt64(&s.totalHTTP2Connections)
	rejectedConns := atomic.LoadInt64(&s.rejectedConnections)
	totalStreams := atomic.LoadInt64(&s.totalStreams)
	uploaded := atomic.LoadInt64(&s.totalBytesUploaded)
	downloaded := atomic.LoadInt64(&s.totalBytesDownloaded)

	log.Printf("📊 === HTTP2服务器最终统计信息 ===")
	log.Printf("⏱️  总运行时间: %v", uptime)
	log.Printf("🔗 处理的HTTP2连接总数: %d", totalConns)
	log.Printf("🎯 处理的HTTP2流总数: %d", totalStreams)
	log.Printf("❌ 拒绝的连接数: %d", rejectedConns)
	log.Printf("📈 总数据传输 - 上传: %s, 下载: %s", formatBytes(uploaded), formatBytes(downloaded))
	if uptime.Minutes() > 0 {
		log.Printf("⚡ 平均流速率: %.2f 流/分钟", float64(totalStreams)/uptime.Minutes())
	}
	log.Printf("========================")
}

// GetStats 获取服务器统计信息
func (s *HTTP2Server) GetStats() map[string]interface{} {
	uptime := time.Since(s.startTime)
	totalConns := atomic.LoadInt64(&s.totalHTTP2Connections)
	activeConns := atomic.LoadInt64(&s.activeHTTP2Connections)
	rejectedConns := atomic.LoadInt64(&s.rejectedConnections)
	totalStreams := atomic.LoadInt64(&s.totalStreams)
	activeStreams := atomic.LoadInt64(&s.activeStreams)
	uploaded := atomic.LoadInt64(&s.totalBytesUploaded)
	downloaded := atomic.LoadInt64(&s.totalBytesDownloaded)

	// 获取流量管理器统计
	var trafficStats map[string]interface{}
	if s.trafficManager != nil {
		trafficStats = s.trafficManager.GetAllStats()
	}

	return map[string]interface{}{
		"uptime": uptime.Seconds(),
		"http2_connections": map[string]int64{
			"total":    totalConns,
			"active":   activeConns,
			"rejected": rejectedConns,
		},
		"streams": map[string]int64{
			"total":  totalStreams,
			"active": activeStreams,
		},
		"traffic": map[string]int64{
			"uploaded":   uploaded,
			"downloaded": downloaded,
		},
		"traffic_detail": trafficStats,
	}
}

// adjustBufferPool 动态调整缓冲池大小
func (s *HTTP2Server) adjustBufferPool(frameSize int) {
	// 更新缓冲池的New函数以使用新的帧大小
	bufferPool.New = func() interface{} {
		return make([]byte, frameSize)
	}
	if s.config.EnableDebug {
		log.Printf("🔧 调整缓冲池大小: %d bytes", frameSize)
	}
}
