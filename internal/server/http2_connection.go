package server

import (
	"bytes"
	"fmt"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"golang.org/x/net/http2"
	"golang.org/x/net/http2/hpack"
)

// HTTP2Connection HTTP2连接处理器
// 实现ConnectionHandler接口，封装单个HTTP2连接的所有状态和行为
type HTTP2Connection struct {
	*BaseConnection

	// 网络连接
	conn net.Conn

	// HTTP2协议组件
	framer      *http2.Framer
	decoder     *hpack.Decoder
	writerMutex sync.Mutex // 保护并发写入

	// 流管理
	streamManager *StreamManager

	// 服务器引用和配置
	server           *HTTP2Server
	peerMaxFrameSize int

	// 连接状态
	mu     sync.RWMutex
	closed bool

	// 统计信息（使用原子操作保证并发安全）
	framesSent     int64
	framesReceived int64

	// 清理函数
	cleanupOnce sync.Once
}

// NewHTTP2Connection 创建新的HTTP2连接
func NewHTTP2Connection(server *HTTP2Server, conn net.Conn) *HTTP2Connection {
	clientAddr := conn.RemoteAddr()
	localAddr := conn.LocalAddr()

	// 创建基础连接，直接绑定服务器context
	base := NewBaseConnection(clientAddr, localAddr, ConnectionTypeHTTP2, server.ctx)

	// 创建HTTP2连接
	h2conn := &HTTP2Connection{
		BaseConnection:   base,
		conn:             conn,
		server:           server,
		peerMaxFrameSize: 16 * 1024, // 默认16KB
		streamManager:    NewStreamManager(server.config.MaxStreams),
		closed:           false,
	}

	// 创建HTTP2协议组件
	h2conn.framer = http2.NewFramer(conn, conn)
	h2conn.decoder = hpack.NewDecoder(4096, nil)

	return h2conn
}

// Start 启动HTTP2连接处理
func (c *HTTP2Connection) Start() error {
	c.SetStatus("starting")

	// 注册到连接管理器
	if err := c.server.connectionManager.AddConnection(c); err != nil {
		c.SetStatus("failed")
		// 如果是达到连接限制，记录统计
		if err.Error() == "max connections reached" {
			atomic.AddInt64(&c.server.rejectedConnections, 1)
		}
		return fmt.Errorf("注册连接失败: %w", err)
	}

	// 验证客户端前言
	if err := c.validateClientPreface(); err != nil {
		c.SetStatus("failed")
		c.server.connectionManager.RemoveConnection(c.GetConnID())
		return fmt.Errorf("客户端前言验证失败: %w", err)
	}

	// 处理初始SETTINGS
	if err := c.handleInitialSettings(); err != nil {
		c.SetStatus("failed")
		c.server.connectionManager.RemoveConnection(c.GetConnID())
		return fmt.Errorf("处理初始设置失败: %w", err)
	}

	c.SetStatus("active")

	// 启动ping保活
	go c.pingKeepAlive()

	// 启动主处理循环
	go c.handleFrames()

	return nil
}

// Stop 停止HTTP2连接处理
func (c *HTTP2Connection) Stop() error {
	c.SetStatus("stopping")
	return c.Close()
}

// Close 关闭HTTP2连接
func (c *HTTP2Connection) Close() error {
	c.cleanupOnce.Do(func() {
		c.mu.Lock()
		c.closed = true
		c.mu.Unlock()

		c.SetStatus("closed")

		// 从AuthState中移除此连接（如果已认证）
		if authState := c.GetAuthStateFromGlobal(); authState != nil {
			authState.RemoveHTTP2Connection(c.GetConnID())
		}

		// 从连接管理器移除
		c.server.connectionManager.RemoveConnection(c.GetConnID())

		// 关闭所有流
		if c.streamManager != nil {
			c.streamManager.Close()
		}

		// 关闭基础连接
		c.BaseConnection.Close()

		// 关闭网络连接
		if c.conn != nil {
			c.conn.Close()
		}
	})

	return nil
}

// IsClosed 检查连接是否已关闭
func (c *HTTP2Connection) IsClosed() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.closed
}

// GetStats 获取HTTP2连接统计（重写以添加协议特定信息）
func (c *HTTP2Connection) GetStats() ConnectionStats {
	stats := c.BaseConnection.GetStats()

	// 添加HTTP2特定统计
	if stats.Extra == nil {
		stats.Extra = make(map[string]interface{})
	}
	stats.Extra["peer_max_frame_size"] = c.peerMaxFrameSize
	stats.Extra["max_streams"] = c.server.config.MaxStreams
	stats.Extra["frames_sent"] = atomic.LoadInt64(&c.framesSent)
	stats.Extra["frames_received"] = atomic.LoadInt64(&c.framesReceived)
	stats.Extra["active_streams"] = c.streamManager.Count()

	return stats
}

// GetConnectionInfo 获取HTTP2连接信息（重写以添加协议特定信息）
func (c *HTTP2Connection) GetConnectionInfo() ConnectionInfo {
	info := c.BaseConnection.GetConnectionInfo()

	// 添加HTTP2特定信息
	info.MaxStreams = c.server.config.MaxStreams
	info.IdleTimeout = c.server.config.IdleTimeout

	// 添加TLS信息（如果可用）
	if tlsConn, ok := c.conn.(*net.TCPConn); ok {
		info.LocalAddr = tlsConn.LocalAddr().String()
	}

	return info
}

// validateClientPreface 验证客户端前言
func (c *HTTP2Connection) validateClientPreface() error {
	preface := make([]byte, len(http2.ClientPreface))
	if _, err := c.conn.Read(preface); err != nil {
		return fmt.Errorf("读取客户端前言失败: %w", err)
	}

	if string(preface) != http2.ClientPreface {
		return fmt.Errorf("无效的客户端前言")
	}

	return nil
}

// handleInitialSettings 处理初始SETTINGS帧
func (c *HTTP2Connection) handleInitialSettings() error {
	// 读取客户端SETTINGS
	frame, err := c.framer.ReadFrame()
	if err != nil {
		return fmt.Errorf("读取SETTINGS帧失败: %w", err)
	}

	settingsFrame, ok := frame.(*http2.SettingsFrame)
	if !ok {
		return fmt.Errorf("期望SETTINGS帧，实际收到: %T", frame)
	}

	// 处理客户端设置
	if val, ok := settingsFrame.Value(http2.SettingMaxFrameSize); ok && val >= 8*1024 {
		c.peerMaxFrameSize = int(val)
		// 动态调整缓冲池大小
		c.server.adjustBufferPool(c.peerMaxFrameSize)
	}

	// 发送服务器SETTINGS + ACK
	c.writerMutex.Lock()
	c.framer.WriteSettings(
		http2.Setting{ID: http2.SettingInitialWindowSize, Val: 1 << 20}, // 1MB
		http2.Setting{ID: http2.SettingMaxConcurrentStreams, Val: uint32(c.server.config.MaxStreams)},
	)
	c.framer.WriteSettingsAck()
	c.writerMutex.Unlock()

	atomic.AddInt64(&c.framesSent, 2) // SETTINGS + ACK

	return nil
}

// handleFrames 主帧处理循环
func (c *HTTP2Connection) handleFrames() {
	defer c.Close()

	for {
		select {
		case <-c.Context().Done():
			return
		default:
		}

		// 设置读取超时
		c.conn.SetReadDeadline(time.Now().Add(c.server.config.IdleTimeout * 2))

		frame, err := c.framer.ReadFrame()
		if err != nil {
			if c.server.config.EnableDebug {
				c.server.eventLogger.ServerError(fmt.Errorf("HTTP2帧读取结束: %w", err))
			}
			return
		}

		atomic.AddInt64(&c.framesReceived, 1)

		// 处理不同类型的帧
		switch f := frame.(type) {
		case *http2.SettingsFrame:
			c.handleSettingsFrame(f)
		case *http2.HeadersFrame:
			c.handleHeadersFrame(f)
		case *http2.DataFrame:
			c.handleDataFrame(f)
		case *http2.PingFrame:
			c.handlePingFrame(f)
		case *http2.RSTStreamFrame:
			c.handleRSTStreamFrame(f)
		case *http2.GoAwayFrame:
			c.server.eventLogger.ServerError(fmt.Errorf("收到GOAWAY帧: %s", c.conn.RemoteAddr()))
			return
		}
	}
}

// handleSettingsFrame 处理SETTINGS帧
func (c *HTTP2Connection) handleSettingsFrame(frame *http2.SettingsFrame) {
	if !frame.Header().Flags.Has(http2.FlagSettingsAck) {
		c.writeSettingsAck()
	}
}

// handleHeadersFrame 处理HEADERS帧（包含认证和目标地址）
func (c *HTTP2Connection) handleHeadersFrame(frame *http2.HeadersFrame) {
	streamID := frame.StreamID

	// 服务器保留流，忽略
	if streamID%2 == 0 {
		return
	}

	// 简化：忽略CONTINUATION帧
	if !frame.HeadersEnded() {
		return
	}

	// 解码头部
	headers, err := c.decoder.DecodeFull(frame.HeaderBlockFragment())
	if err != nil {
		c.server.eventLogger.ServerError(fmt.Errorf("[Stream %d] 解码头部失败: %w", streamID, err))
		c.writeRSTStream(streamID, http2.ErrCodeCompression)
		return
	}

	// 提取认证token和目标地址
	var targetAddr, authToken string
	for _, header := range headers {
		switch header.Name {
		case "x-target":
			targetAddr = header.Value
		case "x-auth-token":
			authToken = header.Value
		}
	}

	// 验证认证token
	userID, success, message := c.handleAuthentication(authToken, streamID)
	if !success {
		c.writeHeaders(streamID, 401, message, true)
		return
	}

	// 检查目标地址
	if targetAddr == "" {
		c.server.eventLogger.ServerError(fmt.Errorf("[Stream %d] 缺少目标地址", streamID))
		c.writeHeaders(streamID, 400, "Missing X-Target", true)
		return
	}

	// 检查流数量限制
	if c.streamManager.CheckLimit() {
		c.server.eventLogger.ServerError(fmt.Errorf("[Stream %d] 达到最大流数限制 (%d)", streamID, c.server.config.MaxStreams))
		c.writeRSTStream(streamID, http2.ErrCodeRefusedStream)
		return
	}

	// 触发TCP请求事件
	c.server.eventLogger.TCPRequest(c.conn.RemoteAddr(), targetAddr, uint64(streamID))

	// 启动隧道
	go c.handleTunnel(streamID, targetAddr, userID, authToken)
}

// handleDataFrame 处理DATA帧
func (c *HTTP2Connection) handleDataFrame(frame *http2.DataFrame) {
	streamID := frame.StreamID

	stream, exists := c.streamManager.Get(streamID)
	if !exists {
		return
	}

	// 转发数据到目标服务器
	if data := frame.Data(); len(data) > 0 {
		stream.dst.SetWriteDeadline(time.Now().Add(c.server.config.IdleTimeout))
		if _, err := stream.dst.Write(data); err != nil {
			c.server.eventLogger.ServerError(fmt.Errorf("[Stream %d] 写入目标服务器失败: %w", streamID, err))
			c.writeRSTStream(streamID, http2.ErrCodeConnect)
			return
		}

		// 更新统计
		stream.uploadBytesAdd(int64(len(data)))
		c.AddTraffic(int64(len(data)), 0)

		// 发送窗口更新
		c.writeWindowUpdate(0, uint32(len(data)))
		c.writeWindowUpdate(streamID, uint32(len(data)))
	}

	// 如果流结束，关闭写端
	if frame.StreamEnded() {
		if tcpConn, ok := stream.dst.(*net.TCPConn); ok {
			tcpConn.CloseWrite()
		}
	}
}

// handlePingFrame 处理PING帧
func (c *HTTP2Connection) handlePingFrame(frame *http2.PingFrame) {
	if !frame.Header().Flags.Has(http2.FlagPingAck) {
		c.writePing(true, frame.Data)
	}
}

// handleRSTStreamFrame 处理RST_STREAM帧
func (c *HTTP2Connection) handleRSTStreamFrame(frame *http2.RSTStreamFrame) {
	streamID := frame.StreamID

	stream := c.streamManager.Delete(streamID)
	if stream != nil {
		stream.dst.Close()
		stream.closeSafely()
		c.RemoveStream()
		c.server.eventLogger.ServerError(fmt.Errorf("[Stream %d] 流被重置", streamID))
	}
}

// 写入方法（简化参数传递）
func (c *HTTP2Connection) writeHeaders(streamID uint32, status int, message string, endStream bool) {
	var headerBuffer bytes.Buffer
	encoder := hpack.NewEncoder(&headerBuffer)
	encoder.WriteField(hpack.HeaderField{Name: ":status", Value: fmt.Sprintf("%d", status)})
	encoder.WriteField(hpack.HeaderField{Name: "content-type", Value: "application/octet-stream"})
	if message != "" {
		encoder.WriteField(hpack.HeaderField{Name: "x-info", Value: message})
	}

	c.writerMutex.Lock()
	c.framer.WriteHeaders(http2.HeadersFrameParam{
		StreamID:      streamID,
		EndHeaders:    true,
		EndStream:     endStream,
		BlockFragment: headerBuffer.Bytes(),
	})
	c.writerMutex.Unlock()

	atomic.AddInt64(&c.framesSent, 1)
}

func (c *HTTP2Connection) writeRSTStream(streamID uint32, code http2.ErrCode) {
	c.writerMutex.Lock()
	c.framer.WriteRSTStream(streamID, code)
	c.writerMutex.Unlock()

	atomic.AddInt64(&c.framesSent, 1)
}

func (c *HTTP2Connection) writePing(ack bool, data [8]byte) {
	c.writerMutex.Lock()
	c.framer.WritePing(ack, data)
	c.writerMutex.Unlock()

	atomic.AddInt64(&c.framesSent, 1)
}

func (c *HTTP2Connection) writeSettingsAck() {
	c.writerMutex.Lock()
	c.framer.WriteSettingsAck()
	c.writerMutex.Unlock()

	atomic.AddInt64(&c.framesSent, 1)
}

func (c *HTTP2Connection) writeWindowUpdate(streamID, increment uint32) {
	c.writerMutex.Lock()
	c.framer.WriteWindowUpdate(streamID, increment)
	c.writerMutex.Unlock()

	atomic.AddInt64(&c.framesSent, 1)
}

func (c *HTTP2Connection) writeData(streamID uint32, endStream bool, data []byte) error {
	c.writerMutex.Lock()
	err := c.framer.WriteData(streamID, endStream, data)
	c.writerMutex.Unlock()

	if err == nil {
		atomic.AddInt64(&c.framesSent, 1)
	}

	return err
}

// pingKeepAlive ping保活
func (c *HTTP2Connection) pingKeepAlive() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.Context().Done():
			return
		case <-ticker.C:
			if c.IsClosed() {
				return
			}
			c.writePing(false, [8]byte{1})
		}
	}
}

// handleAuthentication 处理HTTP2认证
func (c *HTTP2Connection) handleAuthentication(authToken string, streamID uint32) (string, bool, string) {
	if authToken == "" {
		c.server.eventLogger.AuthFailure(c.conn.RemoteAddr(), "", "Missing auth token")
		return "", false, "Missing X-Auth-Token header"
	}

	// 使用AuthManager进行认证，传递连接处理器实例
	userID, success, message := c.server.authManager.AuthenticateHTTP2Token(authToken, "", c.conn.RemoteAddr(), c)

	if success {
		// 触发认证成功事件
		c.server.eventLogger.AuthSuccess(c.conn.RemoteAddr(), userID)
		c.SetAuthInfo(userID, "") // 设置认证信息到BaseConnection
	} else {
		// 触发认证失败事件
		c.server.eventLogger.AuthFailure(c.conn.RemoteAddr(), authToken, message)
	}

	return userID, success, message
}

// handleTunnel 处理隧道连接
func (c *HTTP2Connection) handleTunnel(streamID uint32, targetAddr, userID, authToken string) {
	// 连接目标服务器
	targetConn, err := c.server.connPool.Get(targetAddr)
	if err != nil {
		c.server.eventLogger.TCPError(c.conn.RemoteAddr(), targetAddr, uint64(streamID), err)
		c.writeHeaders(streamID, 502, "Bad Gateway", true)
		return
	}

	connectStart := time.Now()
	connectDuration := time.Since(connectStart)

	// 触发TCP连接建立事件
	c.server.eventLogger.TCPConnect(c.conn.RemoteAddr(), targetAddr, uint64(streamID), connectDuration)

	// 发送200 OK响应
	c.writeHeaders(streamID, 200, "OK", false)

	// 创建流状态
	streamState := &HTTP2StreamState{
		dst:        targetConn,
		done:       make(chan struct{}),
		targetAddr: targetAddr,
		userID:     userID,
		authToken:  authToken,
		startTime:  time.Now(),
		server:     c.server,
	}

	// 注册流
	if err := c.streamManager.Add(streamID, streamState); err != nil {
		c.server.eventLogger.ServerError(fmt.Errorf("[Stream %d] 注册流失败: %w", streamID, err))
		targetConn.Close()
		return
	}

	// 统计
	c.AddStream()
	atomic.AddInt64(&c.server.totalStreams, 1)
	atomic.AddInt64(&c.server.activeStreams, 1)

	// 启动读取泵（目标服务器 → 客户端）
	go c.readPump(streamState, streamID)

	// 等待结束
	select {
	case <-streamState.done:
	case <-c.Context().Done():
	}

	// 清理
	c.server.connPool.Put(targetAddr, targetConn)
	c.streamManager.Delete(streamID)
	c.RemoveStream()

	atomic.AddInt64(&c.server.activeStreams, -1)

	// 触发TCP关闭事件
	duration := time.Since(streamState.startTime)
	c.server.eventLogger.TCPClose(c.conn.RemoteAddr(), targetAddr, uint64(streamID), streamState.BytesUp, streamState.BytesDown, duration)
}

// readPump 读取泵：从目标服务器读取数据并发送到客户端
func (c *HTTP2Connection) readPump(streamState *HTTP2StreamState, streamID uint32) {
	// 注册到StreamManager的WaitGroup
	c.streamManager.wg.Add(1)
	defer c.streamManager.wg.Done()
	defer streamState.closeSafely()

	// 从缓冲池获取缓冲区
	buffer := bufferPool.Get().([]byte)
	defer bufferPool.Put(buffer)

	// 如果缓冲区大小不匹配，创建新的缓冲区
	if len(buffer) != c.peerMaxFrameSize {
		buffer = make([]byte, c.peerMaxFrameSize)
	}

	for {
		select {
		case <-c.Context().Done():
			return // 优雅退出
		default:
		}

		streamState.dst.SetReadDeadline(time.Now().Add(c.server.config.IdleTimeout))
		n, err := streamState.dst.Read(buffer)

		if n > 0 {
			// 发送数据到客户端
			data := buffer[:n]
			if err := c.writeData(streamID, false, data); err != nil {
				c.server.eventLogger.ServerError(fmt.Errorf("[Stream %d] 发送数据失败: %w", streamID, err))
				return
			}

			// 更新统计
			streamState.downloadBytesAdd(int64(n))
			c.AddTraffic(0, int64(n))
		}

		if err != nil {
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				continue
			}
			// 发送流结束标志
			c.writeData(streamID, true, nil)
			return
		}
	}
}

// SetAuthToken 设置认证token（实现ConnectionHandler接口）
func (c *HTTP2Connection) SetAuthToken(token string) {
	c.BaseConnection.SetAuthToken(token)
}

// GetAuthStateFromGlobal 获取AuthState（实现ConnectionHandler接口）
func (c *HTTP2Connection) GetAuthStateFromGlobal() *AuthState {
	return c.BaseConnection.GetAuthStateFromGlobal()
}
