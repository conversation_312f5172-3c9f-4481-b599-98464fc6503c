package main

import (
	"crypto/tls"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"socks5-quic-server/internal/server"
)

func main() {
	// 创建多协议服务器配置
	config := &server.MultiProtocolConfig{
		MaxConnections:  100,
		IdleTimeout:     5 * time.Minute,
		CleanupInterval: 1 * time.Minute,
		StatsInterval:   30 * time.Second,
		EnableDebug:     true,

		// 启用QUIC服务
		QUICEnabled:    true,
		QUICListenAddr: "0.0.0.0:8443",
		QUICTLSConfig: &tls.Config{
			InsecureSkipVerify: true,
		},

		// 认证配置
		AuthConfig: &server.AuthConfig{
			Mode:    "token",
			Timeout: 10,
		},
	}

	// 创建多协议服务器
	multiServer, err := server.NewMultiProtocolServer(config)
	if err != nil {
		log.Fatalf("❌ 创建服务器失败: %v", err)
	}

	// 启动服务器
	if err := multiServer.Start(); err != nil {
		log.Fatalf("❌ 启动服务器失败: %v", err)
	}

	log.Printf("🚀 测试服务器启动成功")

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待关闭信号
	sig := <-sigChan
	log.Printf("🔔 收到信号: %v", sig)

	// 记录关闭开始时间
	shutdownStart := time.Now()
	log.Printf("🔄 开始关闭服务器...")

	// 关闭服务器
	if err := multiServer.Stop(); err != nil {
		log.Printf("❌ 关闭服务器时出错: %v", err)
	}

	// 记录关闭完成时间
	shutdownDuration := time.Since(shutdownStart)
	log.Printf("✅ 服务器关闭完成，耗时: %v", shutdownDuration)

	// 验证关闭时间是否合理（应该在几秒内完成）
	if shutdownDuration > 5*time.Second {
		log.Printf("⚠️  关闭时间过长: %v，可能存在问题", shutdownDuration)
	} else {
		log.Printf("🎉 关闭时间正常: %v，context传播工作正常", shutdownDuration)
	}
}
