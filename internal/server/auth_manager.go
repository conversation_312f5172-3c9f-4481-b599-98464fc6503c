package server

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"net"
	"sync"
	"sync/atomic"
	"time"

	"socks5-quic-server/internal/protocol"
)

// 预定义错误变量
var (
	// 服务器相关错误
	ErrServerShutdown     = errors.New("server is shutting down")
	ErrAuthTimeout        = errors.New("authentication timeout")
	ErrAPIClientNotConfig = errors.New("API client not configured")

	// 认证相关错误
	ErrInvalidToken  = errors.New("invalid token")
	ErrTokenRequired = errors.New("token is required")
)

// AuthResult 统一的认证结果
type AuthResult struct {
	AuthState *AuthState
	Success   bool
	Error     error
	Message   string
}

// authWaiter 认证等待结构，支持广播给多个等待者
type authWaiter struct {
	done      chan struct{} // 完成信号，关闭时广播给所有等待者
	closeOnce sync.Once     // 确保done只被关闭一次
}

type TrafficEventType int

const (
	TrafficEventTypeNormal  TrafficEventType = 1
	TrafficEventTypeOffline TrafficEventType = 3
)

// AuthConfig 认证配置
type AuthConfig struct {
	Mode                      string        `json:"mode"`                         // 认证模式: "token", "api", "hybrid"
	APIUrl                    string        `json:"api_url"`                      // API认证服务URL
	APIKey                    string        `json:"api_key"`                      // API密钥
	Timeout                   int           `json:"timeout"`                      // 认证超时时间（秒）
	CacheSize                 int           `json:"cache_size"`                   // 认证缓存大小
	CacheTTL                  int           `json:"cache_ttl"`                    // 缓存过期时间（秒）
	UserTrafficReportInterval time.Duration `json:"user_traffic_report_interval"` // 用户流量上报间隔
}

// QUICConnection 已废弃，使用 QUICConnectionHandler 替代

// AuthState 认证状态
type AuthState struct {
	Ip           string    `json:"ip"`
	UserID       string    `json:"user_id"`
	Token        string    `json:"token"`
	ClientID     string    `json:"client_id"`
	IsAuth       bool      `json:"is_auth"`
	AuthTime     time.Time `json:"auth_time"`
	ExpiresAt    time.Time `json:"expires_at"` //目前忽略
	LastActivity time.Time `json:"last_activity"`
	StreamCount  int       `json:"stream_count"`
	BytesUp      int64     `json:"bytes_up"`
	BytesDown    int64     `json:"bytes_down"`

	reportTicker   *time.Ticker  `json:"-"`
	reportStopCh   chan struct{} `json:"-"`
	lastReportUp   int64         `json:"-"` // 上次上报的上传字节数
	lastReportDown int64         `json:"-"` // 上次上报的下载字节数
	reportMutex    sync.RWMutex  `json:"-"` // 保护上报状态的锁
	reportStarted  bool          `json:"-"` // 明确的启动状态
	stopOnce       sync.Once     `json:"-"` // 确保只停止一次上报

	QUICConn  *QUICConnectionHandler `json:"-"` // 当前的QUIC连接处理器
	connMutex sync.RWMutex           `json:"-"` // 保护连接的锁

	// HTTP2连接集合管理
	HTTP2Connections map[string]*HTTP2Connection `json:"-"` // HTTP2连接集合，key为连接ID
	http2Mutex       sync.RWMutex                `json:"-"` // 保护HTTP2连接集合的锁

	destroyOnce sync.Once `json:"-"` // 确保只销毁一次
	destroyed   bool      `json:"-"` // 销毁状态标志

}

// SetQUICConnectionHandlerSafely 原子地替换QUIC连接，返回旧连接
func (as *AuthState) SetQUICConnectionHandlerSafely(newConn *QUICConnectionHandler) *QUICConnectionHandler {
	as.connMutex.Lock()
	defer as.connMutex.Unlock()

	// 在锁保护下，原子地完成：读取旧连接 + 设置新连接 + 更新时间
	oldConn := as.QUICConn
	as.QUICConn = newConn
	as.LastActivity = time.Now()

	return oldConn // 返回旧连接，让调用方决定是否关闭
}

func (as *AuthState) GetQUICConnectionHandler() *QUICConnectionHandler {
	as.connMutex.RLock()
	defer as.connMutex.RUnlock()

	return as.QUICConn
}

// AddHTTP2Connection 添加HTTP2连接到集合
func (as *AuthState) AddHTTP2Connection(conn *HTTP2Connection) {
	as.http2Mutex.Lock()
	defer as.http2Mutex.Unlock()

	// 初始化map（如果还没有初始化）
	if as.HTTP2Connections == nil {
		as.HTTP2Connections = make(map[string]*HTTP2Connection)
	}

	connID := conn.GetConnID()

	// 检查是否已存在，避免重复添加
	if _, exists := as.HTTP2Connections[connID]; exists {
		log.Printf("⚠️  HTTP2连接已存在，跳过重复添加: Token=%s, ConnID=%s, UserID=%s",
			maskToken(as.Token), connID, as.UserID)
		return
	}

	as.HTTP2Connections[connID] = conn
	log.Printf("🔗 添加HTTP2连接到AuthState: Token=%s, ConnID=%s, UserID=%s, 当前连接数=%d",
		maskToken(as.Token), connID, as.UserID, len(as.HTTP2Connections))
}

// RemoveHTTP2Connection 从集合中移除HTTP2连接
func (as *AuthState) RemoveHTTP2Connection(connID string) {
	as.http2Mutex.Lock()
	defer as.http2Mutex.Unlock()

	if as.HTTP2Connections != nil {
		if _, exists := as.HTTP2Connections[connID]; exists {
			delete(as.HTTP2Connections, connID)
			log.Printf("🗑️  从AuthState移除HTTP2连接: Token=%s, ConnID=%s, UserID=%s, 剩余连接数=%d",
				maskToken(as.Token), connID, as.UserID, len(as.HTTP2Connections))
		}
	}
}

// GetHTTP2Connections 获取所有HTTP2连接（返回副本，避免并发问题）
func (as *AuthState) GetHTTP2Connections() map[string]*HTTP2Connection {
	as.http2Mutex.RLock()
	defer as.http2Mutex.RUnlock()

	if as.HTTP2Connections == nil {
		return make(map[string]*HTTP2Connection)
	}

	// 返回副本
	result := make(map[string]*HTTP2Connection)
	for k, v := range as.HTTP2Connections {
		result[k] = v
	}
	return result
}

// CloseAllHTTP2Connections 关闭所有HTTP2连接
func (as *AuthState) CloseAllHTTP2Connections() {
	as.http2Mutex.Lock()
	defer as.http2Mutex.Unlock()

	if as.HTTP2Connections == nil {
		return
	}

	count := len(as.HTTP2Connections)
	if count == 0 {
		return
	}

	log.Printf("🔌 开始关闭AuthState中的所有HTTP2连接: Token=%s, UserID=%s, 连接数=%d",
		maskToken(as.Token), as.UserID, count)

	// 关闭所有连接
	for connID, conn := range as.HTTP2Connections {
		if conn != nil && !conn.IsClosed() {
			conn.Close()
			log.Printf("🔌 HTTP2连接已关闭: ConnID=%s", connID)
		}
	}

	// 清空集合
	as.HTTP2Connections = make(map[string]*HTTP2Connection)
	log.Printf("✅ 所有HTTP2连接关闭完成: Token=%s, UserID=%s", maskToken(as.Token), as.UserID)
}

// 销毁AuthState（幂等性保证，并发安全）
func (as *AuthState) Destroy() {
	as.destroyOnce.Do(func() {
		log.Printf("🗑️  开始销毁AuthState: UserID=%s, Token=%s", as.UserID, maskToken(as.Token))

		// 先停止上报（在连接还可用时进行最后上报）
		as.StopReporting()

		// 关闭所有HTTP2连接
		as.CloseAllHTTP2Connections()

		// 然后关闭QUIC连接
		as.connMutex.Lock()
		defer as.connMutex.Unlock()
		if as.QUICConn != nil {
			as.QUICConn.Close()
			as.QUICConn = nil
			log.Printf("🔌 QUIC连接已关闭: UserID=%s", as.UserID)
		}

		// 标记为已销毁
		as.destroyed = true
		log.Printf("✅ AuthState销毁完成: UserID=%s", as.UserID)
	})
}

// StartReporting 启动定时上报（并发安全）
func (as *AuthState) StartReporting(interval time.Duration) {
	as.reportMutex.Lock()
	defer as.reportMutex.Unlock()

	if as.reportStarted {
		return // 已经启动了
	}

	as.reportTicker = time.NewTicker(interval)
	as.reportStopCh = make(chan struct{})
	as.lastReportUp = atomic.LoadInt64(&as.BytesUp)
	as.lastReportDown = atomic.LoadInt64(&as.BytesDown)
	as.reportStarted = true

	go as.reportLoop()
}

// StopReporting 停止定时上报（使用stopOnce确保只执行一次）
func (as *AuthState) StopReporting() {
	as.stopOnce.Do(func() {
		as.reportMutex.Lock()
		defer as.reportMutex.Unlock()

		if !as.reportStarted {
			return // 未启动，无需停止
		}

		// 停止ticker
		if as.reportTicker != nil {
			as.reportTicker.Stop()
			// 不设置为 nil，避免 reportLoop 中的空指针访问
		}

		// 关闭停止通道
		if as.reportStopCh != nil {
			close(as.reportStopCh)
			as.reportStopCh = nil
		}

		as.reportStarted = false

		// 最后上报（移到锁外，避免死锁）
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.Printf("❌ 最后上报时发生异常: %v", r)
				}
			}()
			as.doReportWithEvent(TrafficEventTypeOffline)
		}()
	})
}

// IsReporting 检查是否正在上报
func (as *AuthState) IsReporting() bool {
	as.reportMutex.RLock()
	defer as.reportMutex.RUnlock()
	return as.reportStarted
}

// IsDestroyed 检查是否已销毁
func (as *AuthState) IsDestroyed() bool {
	as.connMutex.RLock()
	defer as.connMutex.RUnlock()
	return as.destroyed
}

// reportLoop 上报循环
func (as *AuthState) reportLoop() {
	for {
		select {
		case <-as.reportTicker.C:
			as.doReport()
		case <-as.reportStopCh:
			return
		}
	}
}

// doReport 执行上报
func (as *AuthState) doReport() {
	as.doReportWithEvent(TrafficEventTypeNormal) // 默认使用 Event: 1 表示正常流量上报
}

// doReportWithEvent 执行上报，指定事件类型
func (as *AuthState) doReportWithEvent(event TrafficEventType) {
	currentUp := atomic.LoadInt64(&as.BytesUp)
	currentDown := atomic.LoadInt64(&as.BytesDown)

	deltaUp := currentUp - as.lastReportUp
	deltaDown := currentDown - as.lastReportDown

	// 对于断开连接事件(Event: TrafficEventTypeOffline)，即使没有增量也要上报
	shouldReport := (deltaUp > 0 || deltaDown > 0) || event == TrafficEventTypeOffline

	if shouldReport {
		eventDesc := "正常流量"
		if event == TrafficEventTypeOffline {
			eventDesc = "用户离线"
		}

		log.Printf("📊 流量上报 - 用户: %s, Token: %s, 事件: %s, 增量: ↑%d ↓%d, 总计: ↑%d ↓%d",
			as.UserID, maskToken(as.Token), eventDesc, deltaUp, deltaDown, currentUp, currentDown)

		if server := GetGlobalServer(); server != nil {
			if apiClient := server.GetAPIClient(); apiClient != nil {

				data := &ReportUserTrafficRequest{
					UserID:    as.UserID,
					BytesUp:   deltaUp,
					BytesDown: deltaDown,
					Timestamp: time.Now().Unix(),
					Token:     as.Token,
					Ip:        as.Ip,
					Event:     event,
					Fd:        1,
				}

				if trafficResp, err := apiClient.ReportUserTraffic(data); err != nil {
					log.Printf("❌ 流量上报失败: %v", err)
				} else {
					log.Printf("✅ 流量上报成功: 用户 %s, 事件: %s, ↑%d ↓%d", as.UserID, eventDesc, deltaUp, deltaDown)

					//如果已经是最后一次上报 就返回 不在关注后续操作
					if event == TrafficEventTypeOffline {
						return
					}

					// 检查用户是否过期
					if trafficResp.Data != nil && trafficResp.Data.MemberIsExpired {
						log.Printf("⚠️  检测到用户会员过期: UserID=%s, 开始清理该用户的所有连接和缓存", as.UserID)

						if server := GetGlobalServer(); server != nil {
							if authManager := server.GetAuthManager(); authManager != nil {
								authManager.RemoveAuthState(as)
								log.Printf("🧹 过期用户对应token缓存清理完成: UserID=%s, Token=%s", as.UserID, maskToken(as.Token))
							}
						}
						return
					}

					// 更新上次上报的数据
					as.lastReportUp = currentUp
					as.lastReportDown = currentDown
				}
			}
		}
	}
}

// AuthManager 认证管理器
type AuthManager struct {
	config *AuthConfig

	authCache    sync.Map // Token -> 缓存的认证状态，使用sync.Map避免死锁
	pendingAuths sync.Map // Token -> *authWaiter，正在进行的认证等待结构

	serverID string

	// Context管理
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewAuthManager 创建认证管理器
func NewAuthManager(parentCtx context.Context, config *AuthConfig) *AuthManager {
	if config == nil {
		config = &AuthConfig{
			Mode:      "token",
			Timeout:   10,
			CacheSize: 1000,
			CacheTTL:  300, // 5分钟
		}
	}

	// 基于父context创建子context
	var ctx context.Context
	var cancel context.CancelFunc
	if parentCtx != nil {
		ctx, cancel = context.WithCancel(parentCtx)
	} else {
		// 兼容性：如果没有父context，使用Background
		ctx, cancel = context.WithCancel(context.Background())
	}

	// 生成服务器ID
	serverIDBytes := make([]byte, 8)
	rand.Read(serverIDBytes)
	serverID := hex.EncodeToString(serverIDBytes)

	return &AuthManager{
		config:   config,
		serverID: serverID,
		ctx:      ctx,
		cancel:   cancel,
	}
}

// AuthenticateConnection 认证QUIC连接
func (am *AuthManager) AuthenticateConnection(quicConn *QUICConnectionHandler, authReq *protocol.AuthRequest) (*protocol.AuthResponse, error) {
	connID := quicConn.GetConnID()
	log.Printf("🔐 开始QUIC认证连接: %s, Token: %s, ClientID: %s", connID, maskToken(authReq.Token), authReq.ClientId)

	// 验证时间戳（防重放攻击）
	if !am.validateTimestamp(authReq.Timestamp) {
		log.Printf("❌ QUIC认证失败 - 时间戳无效: %s", connID)
		return protocol.NewAuthResponse(
			protocol.AuthStatusInvalid,
			"", "Invalid timestamp", am.serverID, 0,
		), nil
	}

	// 调用核心认证逻辑
	result := am.authenticateTokenCore(authReq.Token, authReq.ClientId, authReq.Ip)

	// 处理QUIC特有逻辑：连接管理
	if result.Success {
		// 原子地替换连接，获取旧连接
		oldConn := result.AuthState.SetQUICConnectionHandlerSafely(quicConn)

		// 如果有旧连接，关闭它
		if oldConn != nil {
			oldConn.Close()
			log.Printf("✅ 强制下线之前的QUIC连接: %s -> %s", oldConn.GetConnID(), result.AuthState.UserID)
		}

		log.Printf("✅ 设置QUIC连接: %s -> %s", quicConn.GetConnID(), result.AuthState.UserID)
	}

	// 转换为QUIC响应格式
	return am.convertToQUICResponse(result, quicConn, authReq)
}

// performAPIAuth 执行API认证
func (am *AuthManager) performAPIAuth(authReq *protocol.AuthRequest) (*AuthState, error) {
	// 通过全局服务器获取API客户端
	var apiClient *APIClient
	if server := GetGlobalServer(); server != nil {
		apiClient = server.GetAPIClient()
	}
	if apiClient == nil {
		return &AuthState{IsAuth: false}, ErrAPIClientNotConfig
	}

	// 构造API请求
	checkReq := &CheckWSLoginRequest{
		Ip:        authReq.Ip,
		Token:     authReq.Token,
		ClientID:  authReq.ClientId,
		Timestamp: int64(authReq.Timestamp),
		Nonce:     fmt.Sprintf("%d", authReq.Nonce),
		ServerID:  am.serverID,
		Fd:        1,
	}

	// 调用API客户端
	apiResp, err := apiClient.CheckWSLogin(checkReq)
	if err != nil {
		return nil, err
	}

	// 构造认证状态
	authState := &AuthState{
		Ip:           authReq.Ip,
		UserID:       apiResp.UserID,
		Token:        authReq.Token,
		ClientID:     authReq.ClientId,
		IsAuth:       apiResp.Success,
		AuthTime:     time.Now(),
		LastActivity: time.Now(),
	}

	if !apiResp.Success {
		return nil, fmt.Errorf(apiResp.Message)
	}

	return authState, nil
}

// createSuccessResponse 创建成功响应
func (am *AuthManager) createSuccessResponse(quicConn *QUICConnectionHandler, authState *AuthState, authReq *protocol.AuthRequest) (*protocol.AuthResponse, error) {
	// 更新认证状态
	authState.Token = authReq.Token
	authState.ClientID = authReq.ClientId
	authState.AuthTime = time.Now()
	authState.LastActivity = time.Now()
	authState.Ip = authReq.Ip

	// 只设置token到连接处理器（连接设置由调用方负责）
	quicConn.SetAuthToken(authState.Token)

	log.Printf("✅ 连接认证成功: %s -> %s", quicConn.GetConnID(), authState.UserID)

	return protocol.NewAuthResponse(
		protocol.AuthStatusSuccess,
		authState.UserID,
		"Authentication successful",
		am.serverID,
		uint64(authState.ExpiresAt.Unix()),
	), nil
}

// UpdateStatsUnified 统一更新方法，同时支持QUIC和HTTP2
func (am *AuthManager) UpdateStatsUnified(event *TrafficEvent) {
	// 更新QUIC连接统计（通过token）
	if event.AuthToken != "" && event.TrafficType == TrafficTypeQUIC {
		authState := am.getCachedAuth(event.AuthToken)
		if authState != nil {
			am.updateAuthStateStats(authState, event.Uploaded, event.Downloaded)
		}
	}

	// 更新HTTP2设备统计（如果有token）
	if event.AuthToken != "" && event.TrafficType == TrafficTypeHTTP2 {
		authState := am.getCachedAuth(event.AuthToken)
		if authState != nil {
			am.updateAuthStateStats(authState, event.Uploaded, event.Downloaded)
		}
	}
}

// updateAuthStateStats 更新认证状态的统计信息（私有方法，提取公共逻辑）
func (am *AuthManager) updateAuthStateStats(authState *AuthState, bytesUp, bytesDown int64) {
	// 使用原子操作更新字节统计，确保并发安全
	atomic.AddInt64(&authState.BytesUp, bytesUp)
	atomic.AddInt64(&authState.BytesDown, bytesDown)
	authState.LastActivity = time.Now()
}

// CleanupExpired 清理不活跃的用户
func (am *AuthManager) CleanupExpired(timeout time.Duration) {
	now := time.Now()
	cleanedCount := 0
	totalCount := 0

	am.authCache.Range(func(key, value interface{}) bool {
		authState := value.(*AuthState)
		totalCount++

		// 额外检查：确保authState的LastActivity确实早于now（防止Range期间新增的数据被误删）
		if authState.LastActivity.Before(now) && now.Sub(authState.LastActivity) > timeout {
			inactiveTime := now.Sub(authState.LastActivity)
			log.Printf("🧹 清理不活跃用户: UserID=%s, Token=%s, 不活跃时间=%v, 超时阈值=%v",
				authState.UserID, maskToken(authState.Token), inactiveTime, timeout)
			am.RemoveAuthState(authState)
			cleanedCount++
		}
		return true
	})

	if cleanedCount > 0 {
		log.Printf("🧹 定时清理完成: 清理了 %d/%d 个不活跃用户", cleanedCount, totalCount)
	}
}

func (am *AuthManager) RemoveAuthState(authState *AuthState) {
	// 检查是否已经销毁，避免重复处理
	if authState.IsDestroyed() {
		log.Printf("⚠️  AuthState已经销毁，跳过重复清理: UserID=%s, Token=%s",
			authState.UserID, maskToken(authState.Token))
		return
	}

	log.Printf("🧹 开始清理AuthState: UserID=%s, Token=%s", authState.UserID, maskToken(authState.Token))

	// 从缓存中删除（根据token删除）
	am.authCache.Delete(authState.Token)

	// 销毁AuthState（幂等性保证）
	authState.Destroy()

	// 原子地检查并删除pendingAuths中的等待器
	// 使用循环重试，确保我们操作的是同一个waiter对象
	for {
		if value, exists := am.pendingAuths.Load(authState.Token); exists {
			waiter := value.(*authWaiter)
			// 尝试原子删除，如果成功说明我们操作的是正确的waiter
			if am.pendingAuths.CompareAndDelete(authState.Token, waiter) {
				// 设置关闭结果并广播关闭信号
				waiter.closeOnce.Do(func() {
					close(waiter.done)
				})
				break
			}
			// 如果CompareAndDelete失败，说明值被其他goroutine修改了，重试
		} else {
			// 不存在，直接退出
			break
		}
	}

	log.Printf("✅ AuthState清理完成: UserID=%s", authState.UserID)
}

// CloseAllConnections 关闭所有连接
func (am *AuthManager) CloseAllConnections() {
	am.authCache.Range(func(key, value interface{}) bool {
		authState := value.(*AuthState)
		// 只处理未销毁的AuthState，避免不必要的调用
		if authState != nil && !authState.IsDestroyed() {
			am.RemoveAuthState(authState)
		}
		return true
	})

	am.authCache = sync.Map{}
	log.Printf("✅ 已强制关闭所有QUIC连接")
}

func (am *AuthManager) GetOnlineTotalUsers() int {
	count := 0
	am.authCache.Range(func(key, value interface{}) bool {
		authState := value.(*AuthState)
		// 只计算有效的（未销毁的）用户
		if authState != nil && !authState.IsDestroyed() {
			count++
		}
		return true
	})
	return count
}

// GetStats 获取认证统计信息
func (am *AuthManager) GetStats() map[string]interface{} {
	connections := make([]map[string]interface{}, 0)
	totalConnections := 0

	am.authCache.Range(func(key, value interface{}) bool {
		authState := value.(*AuthState)
		// 只处理有效的（未销毁的）AuthState
		if authState != nil && !authState.IsDestroyed() {
			connStats := map[string]interface{}{
				"connection_id": key,
				"user_id":       authState.UserID,
				"client_id":     authState.ClientID,
				"auth_time":     authState.AuthTime.Unix(),
				"expires_at":    authState.ExpiresAt.Unix(),
				"last_activity": authState.LastActivity.Unix(),
				"stream_count":  authState.StreamCount,
				"bytes_up":      authState.BytesUp,
				"bytes_down":    authState.BytesDown,
			}
			connections = append(connections, connStats)
			totalConnections++
		}
		return true
	})

	stats := map[string]interface{}{
		"total_connections": totalConnections,
		"connections":       connections,
	}

	return stats
}

func (am *AuthManager) validateTimestamp(timestamp uint64) bool {
	now := uint64(time.Now().Unix())
	diff := int64(now) - int64(timestamp)

	// 允许±5分钟的时间偏差
	return diff >= -300 && diff <= 300
}

func (am *AuthManager) getCachedAuth(token string) *AuthState {
	if value, exists := am.authCache.Load(token); exists {
		authState := value.(*AuthState)
		// 检查是否已销毁，如果已销毁则从缓存中移除并返回nil
		if authState.IsDestroyed() {
			log.Printf("⚠️  发现已销毁的AuthState，从缓存中清理: UserID=%s, Token=%s",
				authState.UserID, maskToken(authState.Token))
			am.authCache.Delete(token)
			return nil
		}
		return authState
	}
	return nil
}

// GetAuthState 公开方法，获取认证状态
func (am *AuthManager) GetAuthState(token string) *AuthState {
	return am.getCachedAuth(token)
}

func (am *AuthManager) cacheAuth(token string, authState *AuthState) {
	// 检查是否正在关闭，如果是则拒绝新的缓存请求
	if server := GetGlobalServer(); server != nil && server.IsClosing() {
		log.Printf("⚠️  服务器正在关闭，拒绝缓存新用户: %s", maskToken(token))
		// 立即销毁，避免资源泄露（这里不需要从缓存删除，因为还没添加到缓存）
		authState.Destroy()
		return
	}

	// 只负责缓存token到AuthState的映射
	am.authCache.Store(token, authState)

	// 启动上报
	authState.StartReporting(am.config.UserTrafficReportInterval)
}

// AuthenticateHTTP2Token 认证HTTP2请求中的Token
// 支持并发控制：同一token的多个并发请求只执行一次认证
func (am *AuthManager) AuthenticateHTTP2Token(token, clientID string, clientAddr net.Addr, connHandler ConnectionHandler) (userID string, success bool, message string) {
	if token == "" {
		log.Printf("❌ HTTP2认证失败 - Token为空: %s", clientAddr.String())
		return "", false, "Token is required"
	}

	log.Printf("🔐 开始HTTP2认证: Token: %s, ClientID: %s, Addr: %s", maskToken(token), clientID, clientAddr.String())

	// 调用核心认证逻辑
	result := am.authenticateTokenCore(token, clientID, clientAddr.String())

	// 处理HTTP2特有逻辑：连接管理
	if result.Success && connHandler != nil {
		// 设置到连接处理器
		connHandler.SetAuthToken(result.AuthState.Token)
		// 添加HTTP2连接到AuthState的连接集合中
		http2Conn := connHandler.(*HTTP2Connection)
		result.AuthState.AddHTTP2Connection(http2Conn)
	}

	// 转换为HTTP2返回格式
	if result.Success {
		log.Printf("✅ HTTP2认证成功: Token: %s -> UserID: %s", maskToken(token), result.AuthState.UserID)
		return result.AuthState.UserID, true, "Authentication successful"
	} else {
		log.Printf("❌ HTTP2认证失败: Token: %s - %s", maskToken(token), result.Message)
		return "", false, result.Message
	}
}

// Stop 停止AuthManager，清理所有资源
func (am *AuthManager) Stop() {
	log.Printf("🛑 正在停止AuthManager...")

	// 取消context，通知所有goroutine停止
	am.cancel()

	// 清理所有pendingAuths
	am.cleanupPendingAuths()

	// 等待所有goroutine结束
	done := make(chan struct{})
	go func() {
		am.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Printf("✅ AuthManager所有goroutine已正常结束")
	case <-time.After(5 * time.Second):
		log.Printf("⚠️  等待AuthManager goroutine超时，强制退出")
	}

	log.Printf("✅ AuthManager已停止")
}

// cleanupPendingAuths 清理所有等待中的认证请求
func (am *AuthManager) cleanupPendingAuths() {
	log.Printf("🧹 清理所有等待中的认证请求...")

	count := 0
	am.pendingAuths.Range(func(key, value interface{}) bool {
		token := key.(string)
		waiter := value.(*authWaiter)

		// 删除并关闭等待器
		am.pendingAuths.Delete(token)
		waiter.closeOnce.Do(func() {
			close(waiter.done)
		})
		count++

		log.Printf("🗑️  清理等待认证: Token=%s", maskToken(token))
		return true
	})

	log.Printf("✅ 清理完成，共处理 %d 个等待中的认证请求", count)
}

// authenticateTokenCore 核心认证逻辑，支持QUIC和HTTP2
func (am *AuthManager) authenticateTokenCore(token, clientID, clientAddr string) *AuthResult {
	// 检查服务器是否正在关闭
	select {
	case <-am.ctx.Done():
		log.Printf("⚠️  服务器正在关闭，拒绝新的认证请求: Token=%s", maskToken(token))
		return &AuthResult{
			Success: false,
			Error:   ErrServerShutdown,
			Message: "Server is shutting down",
		}
	default:
	}

	log.Printf("🔐 开始认证: Token: %s, ClientID: %s, Addr: %s", maskToken(token), clientID, clientAddr)

	// 检查缓存
	if cachedAuth := am.getCachedAuth(token); cachedAuth != nil && cachedAuth.IsAuth {
		// 更新活动时间
		cachedAuth.LastActivity = time.Now()
		log.Printf("✅ 缓存认证成功: Token: %s -> UserID: %s", maskToken(token), cachedAuth.UserID)
		return &AuthResult{
			AuthState: cachedAuth,
			Success:   true,
			Message:   "Authentication successful",
		}
	}

	// 检查是否有正在进行的认证，如果没有则创建新的等待器
	waiter := &authWaiter{
		done: make(chan struct{}),
	}
	actualWaiter, loaded := am.pendingAuths.LoadOrStore(token, waiter)

	if loaded {
		// 有正在进行的认证，等待结果
		existingWaiter := actualWaiter.(*authWaiter)
		log.Printf("⏳ 等待认证结果: Token: %s", maskToken(token))

		// 等待认证完成，设置超时，同时监听服务器关闭信号
		select {
		case <-existingWaiter.done:
			// 认证完成，从缓存中获取结果
			authState := am.getCachedAuth(token)
			if authState != nil && authState.IsAuth {
				log.Printf("✅ 等待认证成功: Token: %s -> UserID: %s", maskToken(token), authState.UserID)
				return &AuthResult{
					AuthState: authState,
					Success:   true,
					Message:   "Authentication successful",
				}
			} else {
				log.Printf("❌ 等待认证失败: Token: %s", maskToken(token))
				return &AuthResult{
					Success: false,
					Error:   ErrInvalidToken,
					Message: "Authentication failed",
				}
			}
		case <-time.After(time.Duration(am.config.Timeout) * time.Second):
			log.Printf("⏰ 认证超时: Token: %s", maskToken(token))
			return &AuthResult{
				Success: false,
				Error:   ErrAuthTimeout,
				Message: "Authentication timeout",
			}
		case <-am.ctx.Done():
			log.Printf("🛑 认证被服务器关闭中断: Token: %s", maskToken(token))
			return &AuthResult{
				Success: false,
				Error:   ErrServerShutdown,
				Message: "Server is shutting down",
			}
		}
	}

	// 确保清理pendingAuths并广播
	defer func() {
		am.pendingAuths.Delete(token)
		waiter.closeOnce.Do(func() {
			close(waiter.done)
		})
	}()

	// 再次检查服务器状态（在开始认证前）
	select {
	case <-am.ctx.Done():
		log.Printf("🛑 认证执行前被服务器关闭中断: Token: %s", maskToken(token))
		return &AuthResult{
			Success: false,
			Error:   ErrServerShutdown,
			Message: "Server is shutting down",
		}
	default:
	}

	log.Printf("🚀 执行认证: Token: %s", maskToken(token))

	// 构造认证请求
	authReq := &protocol.AuthRequest{
		Version:   1,
		Token:     token,
		ClientId:  clientID,
		Timestamp: uint64(time.Now().Unix()),
		Nonce:     0, // HTTP2不需要nonce，QUIC会重新设置
		Ip:        clientAddr,
	}

	// 执行认证
	authState, err := am.performAPIAuth(authReq)
	if err != nil {
		log.Printf("❌ 认证服务错误: Token: %s - %v", maskToken(token), err)
		return &AuthResult{
			Success: false,
			Error:   err,
			Message: err.Error(),
		}
	}

	if !authState.IsAuth {
		log.Printf("❌ 认证失败: Token: %s - Invalid token", maskToken(token))
		return &AuthResult{
			Success: false,
			Error:   ErrInvalidToken,
			Message: "Invalid token",
		}
	}

	// 认证成功，缓存结果
	authState.Token = token
	authState.ClientID = clientID
	authState.AuthTime = time.Now()
	authState.LastActivity = time.Now()
	authState.Ip = clientAddr

	// 缓存认证状态
	am.cacheAuth(token, authState)

	log.Printf("✅ 认证成功: Token: %s -> UserID: %s", maskToken(token), authState.UserID)
	return &AuthResult{
		AuthState: authState,
		Success:   true,
		Message:   "Authentication successful",
	}
}

// convertToQUICResponse 将AuthResult转换为QUIC响应
func (am *AuthManager) convertToQUICResponse(result *AuthResult, quicConn *QUICConnectionHandler, authReq *protocol.AuthRequest) (*protocol.AuthResponse, error) {
	if result.Success {
		return am.createSuccessResponse(quicConn, result.AuthState, authReq)
	}

	// 根据错误类型返回不同的响应状态
	switch {
	case errors.Is(result.Error, ErrServerShutdown):
		return protocol.NewAuthResponse(protocol.AuthStatusServerError, "", result.Message, am.serverID, 0), nil
	case errors.Is(result.Error, ErrAuthTimeout):
		return protocol.NewAuthResponse(protocol.AuthStatusServerError, "", result.Message, am.serverID, 0), nil
	case errors.Is(result.Error, ErrInvalidToken):
		return protocol.NewAuthResponse(protocol.AuthStatusFailed, "", result.Message, am.serverID, 0), nil
	case errors.Is(result.Error, ErrAPIClientNotConfig):
		return protocol.NewAuthResponse(protocol.AuthStatusServerError, "", result.Message, am.serverID, 0), nil
	default:
		// API错误或其他未知错误，默认为服务器错误
		return protocol.NewAuthResponse(protocol.AuthStatusServerError, "", result.Message, am.serverID, 0), nil
	}
}
