package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"socks5-quic-server/internal/server"
)

func testListenerClose() {
	log.Printf("🧪 测试监听器立即关闭功能...")

	// 创建测试配置
	config := &server.MultiProtocolConfig{
		QUICEnabled:     true,
		QUICListenAddr:  "0.0.0.0:8443",
		HTTP2Enabled:    true,
		HTTP2ListenAddr: "0.0.0.0:8444",
		MaxConnections:  100,
		AuthConfig: &server.AuthConfig{
			Mode:      "token",
			Timeout:   10,
			CacheSize: 1000,
			CacheTTL:  300,
			APIKey:    "test-key",
			APIUrl:    "http://localhost:8080",
		},
		EnableDebug: true,
	}

	// 创建服务器
	srv, err := server.NewMultiProtocolServer(config)
	if err != nil {
		log.Fatalf("❌ 创建服务器失败: %v", err)
	}

	// 启动服务器
	log.Printf("🚀 启动测试服务器...")
	if err := srv.Start(); err != nil {
		log.Fatalf("❌ 启动服务器失败: %v", err)
	}

	// 等待2秒让服务器完全启动
	log.Printf("⏰ 等待服务器启动...")
	time.Sleep(2 * time.Second)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	log.Printf("✅ 测试服务器已启动，等待中断信号...")
	log.Printf("📝 请按 Ctrl+C 测试优雅关闭功能")

	// 等待信号
	sig := <-sigChan
	log.Printf("📨 收到信号: %v", sig)

	// 记录关闭开始时间
	shutdownStart := time.Now()
	log.Printf("🛑 开始关闭测试 - %s", shutdownStart.Format("15:04:05.000"))

	// 停止服务器
	if err := srv.Stop(); err != nil {
		log.Printf("❌ 停止服务器时出错: %v", err)
	}

	// 记录关闭完成时间
	shutdownEnd := time.Now()
	duration := shutdownEnd.Sub(shutdownStart)
	log.Printf("✅ 关闭测试完成 - %s (耗时: %v)", shutdownEnd.Format("15:04:05.000"), duration)

	// 验证关闭时间
	if duration < 15*time.Second {
		log.Printf("🎉 测试通过：关闭时间 %v < 15秒", duration)
	} else {
		log.Printf("⚠️  测试警告：关闭时间 %v >= 15秒", duration)
	}

	log.Printf("🏁 测试结束")
}

func main() {
	testListenerClose()
}
